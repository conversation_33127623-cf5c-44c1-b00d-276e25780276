import 'package:flutter/material.dart';
import 'package:pinim/src/QrcodeWidget.dart';

class QrCodeWithLogoExample extends StatelessWidget {
  const QrCodeWithLogoExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('二维码Logo示例'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 不带logo的二维码
            const Text(
              '普通二维码',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const QrCodeWidget(
              userData: 'https://example.com',
              size: 200,
            ),
            
            const SizedBox(height: 32),
            
            // 带图标logo的二维码
            const Text(
              '带图标Logo的二维码',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            QrCodeWidget(
              userData: 'https://example.com',
              size: 200,
              logo: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
            
            const SizedBox(height: 32),
            
            // 带文字logo的二维码
            const Text(
              '带文字Logo的二维码',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            QrCodeWidget(
              userData: 'https://example.com',
              size: 200,
              logo: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'LOGO',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
