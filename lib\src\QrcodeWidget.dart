import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';

/// 可复用的二维码组件
class QrCodeWidget extends StatelessWidget {
  /// 二维码数据
  final String userData;

  /// 二维码大小
  final double size;


  /// 背景颜色
  final Color backgroundColor;

  /// 前景颜色（二维码颜色）
  final Color foregroundColor;

  /// 容器背景颜色
  final Color containerColor;

  /// 圆角半径
  final double borderRadius;



  /// 纠错级别 (0=L, 1=M, 2=Q, 3=H)
  final int errorCorrectLevel;

  /// 中间的logo组件（可选）
  final Widget? logo;

  const QrCodeWidget({
    super.key,
    required this.userData,
    this.size = 240,

    this.backgroundColor = Colors.white,
    this.foregroundColor = Colors.black,
    this.containerColor = Colors.white,
    this.borderRadius = 12,
    this.errorCorrectLevel = 1, // M级纠错（15%）
    this.logo, // 可选的logo参数
  });

  @override
  Widget build(BuildContext context) {
    // 如果需要显示容器
    // if (showContainer) {
    //   return Container(
    //     width: size,
    //     height: size,
    //     decoration: BoxDecoration(
    //       color: containerColor,
    //       borderRadius: BorderRadius.circular(borderRadius),
    //       boxShadow: [
    //         BoxShadow(
    //           color: Colors.black.withOpacity(1),
    //           blurRadius: 10,
    //           offset: const Offset(0, 2),
    //         ),
    //       ],
    //     ),
    //     child: _buildQrCode(),
    //   );
    // }
    // 直接返回二维码
    return _buildQrCode();
  }
  /// 保存二维码到相册
  Future<bool> saveQrCodeToGallery({double? imageSize}) async {
    try {
      final targetSize = imageSize ?? 1024.0;
      
      final painter = QrPainter(
        data: userData,
        version: QrVersions.auto,
        errorCorrectionLevel: errorCorrectLevel,
        color: foregroundColor,
        emptyColor: backgroundColor,
        gapless: false,
      );
 
      // 生成图像数据
      final picData = await painter.toImageData(targetSize);
      
      if (picData != null) {
        final bytes = picData.buffer.asUint8List();
        
        // 保存到相册
        final result = await ImageGallerySaver.saveImage(
          bytes,
          quality: 100,
          name: "qrcode_${DateTime.now().millisecondsSinceEpoch}",
        );
        
        return result['isSuccess'] == true;
      }
      
      return false;
    } catch (e) {
      print('保存二维码失败: $e');
      return false;
    }
  }

  /// 测试保存方法 - 用于调试
  Future<bool> saveQrCodeToGalleryDebug({double? imageSize}) async {
    try {
      final targetSize = imageSize ?? 2048.0; // 使用更大尺寸
      
      print('保存二维码数据: $userData');
      print('目标尺寸: $targetSize');
      
      final painter = QrPainter(
        data: userData,
        version: QrVersions.auto,
        errorCorrectionLevel: errorCorrectLevel,
        color: foregroundColor,
        emptyColor: backgroundColor,
        gapless: false,
      ); 

      final picData = await painter.toImageData(targetSize);
      
      if (picData != null) {
        final bytes = picData.buffer.asUint8List();
        print('生成图像大小: ${bytes.length} bytes');
        
        final result = await ImageGallerySaver.saveImage(
          bytes,
          quality: 100,
          name: "qrcode_debug_${DateTime.now().millisecondsSinceEpoch}",
        );
        
        print('保存结果: $result');
        return result['isSuccess'] == true;
      }
      
      return false;
    } catch (e) {
      print('保存二维码失败: $e');
      return false;
    }
  }

  /// 获取二维码的base64字符串
  Future<String> getQrCodeBase64({double? imageSize}) async {
    try {
      final targetSize = imageSize ?? 1024.0;

      final painter = QrPainter(
        data: userData, 
        version: QrVersions.auto,
        errorCorrectionLevel: errorCorrectLevel,
        color: foregroundColor,
        emptyColor: backgroundColor,
        gapless: false,
      );

      final picData = await painter.toImageData(targetSize);
      
      if (picData != null) {
        final bytes = picData.buffer.asUint8List();
        return base64Encode(bytes);
      }

      throw Exception('Failed to generate image data');
    } catch (e) {
      print('QR Code generation error: $e');
      throw Exception('Failed to generate QR code: $e');
    }
  }

  Widget _buildQrCode() {
    final qrCode = QrImageView(
      data: userData,
      version: QrVersions.auto,
      size: size,
      backgroundColor: backgroundColor,
      // 注意：QrImageView 不直接支持 foregroundColor
      // 需要使用 eyeStyle 和 dataModuleStyle 来设置颜色
      errorCorrectionLevel: errorCorrectLevel, // 使用 int 类型
      eyeStyle: QrEyeStyle(
        eyeShape: QrEyeShape.square,
        color: foregroundColor,
      ),
      dataModuleStyle: QrDataModuleStyle(
        dataModuleShape: QrDataModuleShape.square,
        color: foregroundColor,
      ),
    );

    // 如果没有logo，直接返回二维码
    if (logo == null) {
      return qrCode;
    }

    // 如果有logo，使用Stack将logo叠加在二维码中间
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          qrCode,
          // logo容器，添加白色背景和圆角
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: logo!,
          ),
        ],
      ),
    );
  }

  /// 使用RepaintBoundary保存二维码
  static Future<bool> saveQrCodeFromWidget(GlobalKey repaintKey) async {
    try {
      RenderRepaintBoundary boundary = repaintKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData != null) {
        Uint8List bytes = byteData.buffer.asUint8List();
        
        final result = await ImageGallerySaver.saveImage(
          bytes,
          quality: 100,
          name: "qrcode_widget_${DateTime.now().millisecondsSinceEpoch}",
        );
        
        return result['isSuccess'] == true;
      }
      
      return false;
    } catch (e) {
      print('保存二维码失败: $e');
      return false;
    }
  }
}
